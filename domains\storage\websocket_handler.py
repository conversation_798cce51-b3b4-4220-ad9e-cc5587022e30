"""
WebSocket handler for storage operations.
Handles storage pickup using pickup_process directly and storage insertion using flow-based approach.
"""

import json
import logging
import asyncio
from fastapi import WebSocket, WebSocketDisconnect
from managers import ws_manager, session_manager
from infrastructure.repositories.storage_repository import StorageRepository

logger = logging.getLogger(__name__)

async def handle_storage_pickup_websocket(websocket: WebSocket, session_id: str):
    """
    Handle WebSocket connection for storage pickup operations.
    Uses pickup_process directly as defined in screen_communication.md

    Args:
        websocket: FastAPI WebSocket connection
        session_id: Session ID for this connection
    """
    logger.info(f"Storage pickup WebSocket handler started for session: {session_id}")

    try:
        # Register WebSocket connection
        await ws_manager.connect(session_id, websocket)
        logger.info(f"Storage pickup WebSocket connected: {session_id}")

        # Get session data
        session = session_manager.get_session(session_id)
        if not session:
            logger.error(f"No session found for {session_id}")
            await ws_manager.send(session_id, {
                "type": "error",
                "message": "Session not found"
            })
            return

        section_id = getattr(session, 'section_id', None)
        requires_payment = getattr(session, 'amount', 0) > 0
        reservation_id = getattr(session, 'reservation_id', None)

        if not section_id:
            logger.error(f"No section_id in session {session_id}")
            await ws_manager.send(session_id, {
                "type": "error",
                "message": "No section ID found"
            })
            return

        # Create message queue for pickup_process
        message_queue = asyncio.Queue()

        # Message handler to route WebSocket messages to pickup_process
        async def handle_websocket_messages():
            while ws_manager.is_connected(session_id):
                try:
                    message = await websocket.receive_text()
                    if not message or message.strip() == "":
                        continue

                    try:
                        data = json.loads(message)
                    except json.JSONDecodeError:
                        await ws_manager.send(session_id, {"type": "error", "message": "Invalid JSON"})
                        continue

                    msg_type = data.get("type")

                    if msg_type == "ping":
                        await ws_manager.send(session_id, {"type": "pong"})
                        continue

                    # Route message to pickup_process
                    await message_queue.put(data)

                except WebSocketDisconnect:
                    logger.info(f"Storage pickup WebSocket disconnected: {session_id}")
                    break
                except Exception as e:
                    logger.error(f"Error processing WebSocket message: {e}")
                    break

        # Start message handler task
        message_task = asyncio.create_task(handle_websocket_messages())

        # Start pickup_process directly from universal process_manager
        from managers.process_manager import pickup_process
        success, successful_sections = await pickup_process(
            sections=[section_id],
            session_id=session_id,
            message_queue=message_queue,
            requires_payment=requires_payment
        )

        # Cancel message handler
        message_task.cancel()

        # Complete storage pickup operation
        if success and successful_sections and reservation_id:
            repo = StorageRepository()
            repo.deactivate_reservation(reservation_id)
            logger.info(f"Deactivated storage reservation {reservation_id} after pickup")

        logger.info(f"Storage pickup completed for session {session_id}: success={success}")

    except Exception as e:
        logger.error(f"Error in storage pickup WebSocket handler: {e}")
    finally:
        # Clean up
        ws_manager.disconnect(session_id)
        logger.info(f"Storage pickup WebSocket handler ended for session: {session_id}")


# Keep the old handler for backward compatibility with storage flow
async def handle_storage_flow_websocket(websocket: WebSocket, session_id: str):
    """
    Handle WebSocket connection for storage flow operations (legacy).
    This is kept for backward compatibility.
    """
    # For storage pickup, redirect to the new simplified handler
    session = session_manager.get_session(session_id)
    if session and getattr(session, 'operation', None) == 'storage_pickup':
        await handle_storage_pickup_websocket(websocket, session_id)
        return

    # For other storage operations, use the old flow-based approach
    from .flow_coordinator import flow_coordinator

    logger.info(f"Storage flow WebSocket handler started for session: {session_id}")

    try:
        await ws_manager.connect(session_id, websocket)
        logger.info(f"Registered WebSocket connection for storage flow session: {session_id}")

        await ws_manager.send(session_id, {
            "type": "flow_status",
            "status": "connected",
            "message": "Připojeno k storage flow"
        })

        asyncio.create_task(flow_coordinator.execute_current_step_async(session_id))

        while ws_manager.is_connected(session_id):
            try:
                message = await websocket.receive_text()
                if not message or message.strip() == "":
                    continue

                try:
                    data = json.loads(message)
                except json.JSONDecodeError:
                    await ws_manager.send(session_id, {"type": "error", "message": "Invalid JSON"})
                    continue

                msg_type = data.get("type")

                if msg_type == "ping":
                    await ws_manager.send(session_id, {"type": "pong"})
                    continue

                success = await flow_coordinator.handle_websocket_message(session_id, data)

                if not success:
                    await ws_manager.send(session_id, {
                        "type": "message_error",
                        "message": f"Failed to handle message type '{msg_type}'"
                    })

            except WebSocketDisconnect:
                break
            except Exception as e:
                logger.error(f"Error processing WebSocket message: {e}")
                await ws_manager.send(session_id, {"type": "error", "message": str(e)})

    except WebSocketDisconnect:
        logger.info(f"Storage flow WebSocket disconnected: {session_id}")
    except Exception as e:
        logger.error(f"Unexpected error in storage flow WebSocket handler: {e}")
    finally:
        ws_manager.disconnect(session_id)
        await flow_coordinator.cleanup_flow(session_id)
        logger.info(f"Storage flow WebSocket connection closed: {session_id}")


async def handle_payment_callback(session_id: str, status: str, message: str = "") -> bool:
    """Handle payment callback for storage flow sessions"""
    logger.info(f"Storage websocket handler: Handling payment callback for storage flow session {session_id}: {status}")

    try:
        # Import flow coordinator for payment callback handling
        from .flow_coordinator import flow_coordinator

        # Forward to flow coordinator
        result = await flow_coordinator.handle_payment_callback(session_id, status, message)
        logger.info(f"Storage websocket handler: Payment callback result for {session_id}: {result}")
        return result

    except Exception as e:
        logger.error(f"Storage websocket handler: Error handling payment callback for storage flow session {session_id}: {e}")
        import traceback
        logger.error(f"Storage websocket handler: Traceback: {traceback.format_exc()}")
        return False
